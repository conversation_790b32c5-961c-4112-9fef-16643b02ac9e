import requests

def post_mastodon_status_with_images(instance_url, access_token, status_text, image_paths):
    """
    Upload multiple images and post a status on Mastodon
    """
    try:
        # Normalize instance URL
        if not instance_url.startswith(('http://', 'https://')):
            instance_url = f'https://{instance_url}'
        instance_url = instance_url.rstrip('/')

        headers = {
            'Authorization': f'Bearer {access_token}'
        }

        media_ids = []
        for image_path in image_paths:
            with open(image_path, 'rb') as img_file:
                files = {
                    'file': img_file
                }
                media_upload_url = f"{instance_url}/api/v2/media"
                media_resp = requests.post(media_upload_url, headers=headers, files=files, timeout=10)
                media_resp.raise_for_status()
                media_data = media_resp.json()
                print(media_data)
                media_ids.append(media_data['id'])

        # Now post status with the media
        post_url = f"{instance_url}/api/v1/statuses"
        data = {
            'status': status_text,
            'media_ids[]': media_ids  # Important: media_ids[] (not just media_ids)
        }

        post_resp = requests.post(post_url, headers=headers, data=data, timeout=10)
        post_resp.raise_for_status()
        print('post_resp',post_resp.json())

        return  True,post_resp.json().get('id')

    except Exception as e:
        print(f"Error: {e}")
        return False,''


# instance_url = "mastodon.social"
# access_token = "8LBW7YeWXpAuMQqx1VtXf1YEz7xNHlKF_fBxo7B2RTk"
# status_text = "This is a test post with multiple images 🖼️"
# image_paths = ["mastodon/123.png"]  

# result = post_mastodon_status_with_images(instance_url, access_token, status_text, image_paths)
# print(result)


